import {
  DepthUpdate,
  OrderbookData,
  OrderbookDataSocketHandle,
} from "@/types/OrderBook";
import rf from "@/services/RequestFactory";
export default class OrderbookEventHandler {
  private onUpdate: (orderbook: OrderbookData) => void;
  private symbol: string;
  private orderbook: OrderbookDataSocketHandle;

  constructor(symbol: string, onUpdate: (orderbook: OrderbookData) => void) {
    this.symbol = symbol.toLowerCase();
    this.onUpdate = onUpdate;
    this.orderbook = {
      lastUpdateId: 0,
      bids: new Map(),
      asks: new Map(),
    };
  }

  // Clear internal orderbook state
  clearOrderbook = () => {
    this.orderbook = {
      lastUpdateId: 0,
      bids: new Map(),
      asks: new Map(),
    };
  };

  initOrderbook = async (callback?: (orderbook: OrderbookData) => void) => {
    try {
      this.clearOrderbook();

      const data = await rf
        .getRequest("OrderbookRequest")
        .getOrderbook(this.symbol);

      const { last_updated_at: lastUpdateId, bids, asks } = data?.data;

      if (!lastUpdateId) {
        console.log("Invalid orderbook data");
        return;
      }

      const orderbook = {
        lastUpdateId,
        bids: new Map(bids.map((bid: string[]) => [bid[0], bid[1]])) as Map<
          string,
          string
        >,
        asks: new Map(asks.map((ask: string[]) => [ask[0], ask[1]])) as Map<
          string,
          string
        >,
      };

      this.orderbook = orderbook;

      const orderbookFormatted = await this.formatAndUpdateOrderbook();
      if (callback) callback(orderbookFormatted);
    } catch (error) {
      console.log("Error fetching snapshot:", error);
    }
  };

  processDepthEvent = (event: DepthUpdate) => {
    const { E: lastUpdateId, s: eventSymbol } = event;

    if (eventSymbol?.toLowerCase() !== this.symbol?.toLowerCase()) {
      return;
    }

    if (!this.orderbook.lastUpdateId) {
      return;
    }

    // Ensure the update is not older than what we have
    if (lastUpdateId <= this.orderbook.lastUpdateId) {
      return;
    }

    // If the U is <= lastUpdateId, this snapshot is too old
    // We need to fetch a new one
    // if (firstUpdateId > this.orderbook.lastUpdateId + 1) {
    //   this.orderbook = {
    //     lastUpdateId: 0,
    //     bids: new Map(),
    //     asks: new Map(),
    //   };
    //   this.initOrderbook();
    //   return;
    // }

    this.updateOrderbookBidsAsks(event.b, "bids");
    this.updateOrderbookBidsAsks(event.a, "asks");

    this.orderbook.lastUpdateId = lastUpdateId;

    this.formatAndUpdateOrderbook();
  };

  updateOrderbookBidsAsks = (
    bidsAskData: [string, string][],
    key: "bids" | "asks"
  ) => {
    for (const [price, quantity] of bidsAskData) {
      if (parseFloat(quantity) === 0) {
        this.orderbook[key].delete(price);
        continue;
      }

      this.orderbook[key].set(price, quantity);
    }
    return this.orderbook[key];
  };

  async formatAndUpdateOrderbook() {
    const orderbookUpdate: OrderbookData = {
      lastUpdateId: this.orderbook.lastUpdateId,
      bids: Array.from(this.orderbook.bids.entries()),
      asks: Array.from(this.orderbook.asks.entries()),
    };

    this.onUpdate(orderbookUpdate);

    return orderbookUpdate;
  }
}
