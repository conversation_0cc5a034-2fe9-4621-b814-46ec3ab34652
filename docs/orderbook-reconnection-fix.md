# OrderBook Socket Reconnection Fix

## Problem Description

The OrderBook component had a critical bug where orderbook data became incorrect after a socket disconnection and reconnection. When the WebSocket connection was lost and then restored, the orderbook state would get out of sync because:

1. Depth updates that occurred during the disconnection period were missed
2. The component continued processing updates from where it left off without refreshing the base state
3. No mechanism existed to detect reconnection events and reinitialize data

## Solution Overview

Implemented a comprehensive solution that:

1. **Detects Socket Reconnection Events**: Added logic to track socket connection state changes
2. **Clears Stale Data**: Ensures both component and handler state are cleared before fetching fresh data
3. **Reinitializes with Fresh Data**: Fetches a complete orderbook snapshot from the API on reconnection
4. **Maintains Data Integrity**: Prevents processing of stale depth updates during the reinitialization process

## Implementation Details

### 1. OrderBook Component Changes (`components/OrderBook/index.tsx`)

#### Added State Tracking
```typescript
// Track previous socket connection state to detect reconnection events
const prevSocketConnectedRef = useRef(socketConnected);
const orderbookHandlerRef = useRef<OrderbookEventHandler | null>(null);
```

#### Added Reconnection Detection Logic
```typescript
useEffect(() => {
  const wasDisconnected = !prevSocketConnectedRef.current;
  const isNowConnected = socketConnected;

  // Detect reconnection: was disconnected and now connected
  if (wasDisconnected && isNowConnected && orderbookHandlerRef.current) {
    console.log("Socket reconnected - reinitializing orderbook data");

    // Clear current orderbook state and fetch fresh data
    orderbookRef.current = {
      lastUpdateId: 0,
      bids: [],
      asks: [],
    };
    
    // Clear handler's internal state and fetch fresh data from API
    orderbookHandlerRef.current.initOrderbook((freshOrderbook) => {
      orderbookRef.current = freshOrderbook;
      setForceRender((prev) => prev + 1);
    });
  }

  // Update previous state reference
  prevSocketConnectedRef.current = socketConnected;
}, [socketConnected]);
```

### 2. OrderbookEventHandler Improvements (`components/OrderBook/services/OrderbookEventHandler.ts`)

#### Added State Clearing Method
```typescript
// Clear internal orderbook state
clearOrderbook = () => {
  this.orderbook = {
    lastUpdateId: 0,
    bids: new Map(),
    asks: new Map(),
  };
};
```

#### Enhanced Initialization Method
```typescript
initOrderbook = async (callback?: (orderbook: OrderbookData) => void) => {
  try {
    // Clear existing state before fetching fresh data
    this.clearOrderbook();
    
    // ... rest of initialization logic
  } catch (error) {
    console.log("Error fetching snapshot:", error);
  }
};
```

## Key Features

### 1. Reconnection Detection
- Uses `useRef` to track previous socket connection state
- Detects the transition from disconnected to connected state
- Only triggers reinitialization on actual reconnection events

### 2. State Management
- Clears both component-level (`orderbookRef`) and handler-level (`this.orderbook`) state
- Ensures no stale data persists during reinitialization
- Forces component re-render after fresh data is loaded

### 3. Error Handling
- Graceful handling of API errors during reinitialization
- Console logging for debugging and monitoring
- Maintains existing error handling patterns

### 4. Performance Optimization
- Only reinitializes when necessary (actual reconnection events)
- Uses callback pattern to update component state efficiently
- Maintains existing throttling and optimization mechanisms

## Testing Strategy

### Manual Testing
1. Start development server and navigate to trading page
2. Monitor browser console for connection events
3. Simulate network disconnection (disable network or close WebSocket)
4. Re-enable network connection
5. Verify console shows "Socket reconnected - reinitializing orderbook data"
6. Confirm orderbook data is accurate after reconnection

### Automated Testing
- Created test file template at `__tests__/components/OrderBook/OrderBook.test.tsx`
- Tests cover reconnection detection, state clearing, and data reinitialization
- Can be used when testing framework is set up

## Benefits

1. **Data Accuracy**: Ensures orderbook data is always synchronized with the server
2. **Reliability**: Handles network interruptions gracefully
3. **User Experience**: Prevents display of incorrect pricing information
4. **Maintainability**: Clean, well-documented code that follows existing patterns
5. **Performance**: Minimal overhead, only activates on reconnection events

## Monitoring and Debugging

- Console logs provide clear indication of reconnection events
- Existing error handling and logging mechanisms are preserved
- Easy to monitor in production environments

## Why Previous State Tracking is Critical

### The Problem with Naive Implementation

A simplified approach without previous state tracking would look like:

```typescript
// PROBLEMATIC - DON'T DO THIS
useEffect(() => {
  if (socketConnected && orderbookHandlerRef.current) {
    console.log("Socket is connected - reinitializing orderbook data");
    orderbookHandlerRef.current.initOrderbook();
  }
}, [socketConnected]);
```

### Issues This Would Cause

1. **Excessive API Calls**: Reinitialization on every render when socket is connected
2. **Performance Degradation**: Unnecessary network requests and state clearing
3. **Data Flickering**: Orderbook would clear and reload frequently
4. **Race Conditions**: Multiple simultaneous API calls could cause data inconsistency

### Scenarios Where socketConnected=true But We Don't Want Reinitialization

1. **Initial Mount**: Component mounts with socket already connected
2. **Component Re-renders**: Parent state changes causing OrderBook re-render
3. **Route Navigation**: Navigating between pages while socket stays connected
4. **State Updates**: Other Redux state changes triggering re-renders

### The Solution: State Transition Detection

```typescript
const wasDisconnected = !prevSocketConnectedRef.current;
const isNowConnected = socketConnected;

// Only reinitialize on actual reconnection events
if (wasDisconnected && isNowConnected && orderbookHandlerRef.current) {
  // Reinitialize...
}
```

This ensures reinitialization **only** happens on the specific transition from `false → true`.

## Future Enhancements

1. Add metrics/analytics for reconnection frequency
2. Implement exponential backoff for failed reinitialization attempts
3. Add user notification for extended disconnection periods
4. Consider implementing partial state recovery for very brief disconnections

## Compatibility

- Fully backward compatible with existing OrderBook functionality
- No breaking changes to component API
- Maintains all existing features and optimizations
- Works with current socket management and broadcast system
